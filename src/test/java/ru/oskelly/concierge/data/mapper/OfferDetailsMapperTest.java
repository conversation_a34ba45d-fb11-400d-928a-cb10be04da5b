package ru.oskelly.concierge.data.mapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.ProductConditionDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OfferDetailsDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OrderInfoDTO;
import ru.oskelly.concierge.controller.dto.miniapps.PriceInfoDTO;
import ru.oskelly.concierge.controller.dto.miniapps.ShopperProposedOfferDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;
import ru.oskelly.concierge.data.model.enums.Currency;
import ru.oskelly.concierge.data.model.enums.OfferStatus;
import ru.oskelly.concierge.data.model.enums.OfferType;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@DisplayName("OfferDetailsMapper Tests")
class OfferDetailsMapperTest {

    private OfferDetailsMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(OfferDetailsMapper.class);
    }

    @Nested
    @DisplayName("toOfferDetailsDTO Tests")
    class ToOfferDetailsDTOTests {

        @Test
        @DisplayName("Should map complete Offer to OfferDetailsDTO successfully")
        void shouldMapCompleteOfferToOfferDetailsDTO() {
            // Given
            Offer offer = createCompleteOffer();

            // When
            OfferDetailsDTO result = mapper.toOfferDetailsDTO(offer);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.id()).isEqualTo(1L);
            assertThat(result.aggregateStatus()).isEqualTo(AggregateOfferStatus.ACTIVE);
            assertThat(result.status()).isEqualTo(OfferStatus.IN_PROGRESS);
            assertThat(result.statusLocalized()).isEqualTo("В работе");
            assertThat(result.statusDescriptionLocalized()).isEqualTo("Заполните предложение и отправьте его менеджеру");
            assertThat(result.expirationDate()).isEqualTo(offer.getValidUntil());
            assertThat(result.creationDate()).isEqualTo(offer.getCreationDate());
            assertThat(result.categoryName()).isEqualTo("Electronics");
            assertThat(result.brandName()).isEqualTo("Apple");
            assertThat(result.materialAttributeName()).isEqualTo("Metal");
            assertThat(result.colorAttributeName()).isEqualTo("Black");
            assertThat(result.modelName()).isEqualTo("iPhone 15");
            assertThat(result.description()).isEqualTo("Test description");
            assertThat(result.ordersInfo()).hasSize(2);
            assertThat(result.proposedOffers()).hasSize(1);
        }

        @Test
        @DisplayName("Should handle null Offer gracefully")
        void shouldHandleNullOffer() {
            // When
            OfferDetailsDTO result = mapper.toOfferDetailsDTO(null);

            // Then
            assertThat(result).isNull();
        }

        @Test
        @DisplayName("Should map Offer with minimal data")
        void shouldMapOfferWithMinimalData() {
            // Given
            Offer offer = Offer.builder()
                    .id(1L)
                    .status(OfferStatus.NEW)
                    .shipment(createMinimalShipment())
                    .build();

            // When
            OfferDetailsDTO result = mapper.toOfferDetailsDTO(offer);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.id()).isEqualTo(1L);
            assertThat(result.aggregateStatus()).isEqualTo(AggregateOfferStatus.ACTIVE);
            assertThat(result.status()).isEqualTo(OfferStatus.NEW);
            assertThat(result.ordersInfo()).isEmpty();
            assertThat(result.proposedOffers()).isEmpty();
        }
    }

    @Nested
    @DisplayName("mapRootStatus Tests")
    class MapRootStatusTests {

        @Test
        @DisplayName("Should map ORDER_PAID to COMPLETED")
        void shouldMapOrderPaidToCompleted() {
            // Given
            Offer offer = Offer.builder().status(OfferStatus.ORDER_PAID).build();

            // When
            AggregateOfferStatus result = mapper.mapRootStatus(offer);

            // Then
            assertThat(result).isEqualTo(AggregateOfferStatus.COMPLETED);
        }

        @Test
        @DisplayName("Should map cancelled statuses to ARCHIVED")
        void shouldMapCancelledStatusesToArchived() {
            // Given
            Offer offer1 = Offer.builder().status(OfferStatus.CANCELLED_BY_EXECUTOR).build();
            Offer offer2 = Offer.builder().status(OfferStatus.CANCELLED_NOT_RELEVANT).build();

            // When
            AggregateOfferStatus result1 = mapper.mapRootStatus(offer1);
            AggregateOfferStatus result2 = mapper.mapRootStatus(offer2);

            // Then
            assertThat(result1).isEqualTo(AggregateOfferStatus.ARCHIVED);
            assertThat(result2).isEqualTo(AggregateOfferStatus.ARCHIVED);
        }

        @Test
        @DisplayName("Should map active statuses to ACTIVE")
        void shouldMapActiveStatusesToActive() {
            // Given
            Offer offer1 = Offer.builder().status(OfferStatus.NEW).build();
            Offer offer2 = Offer.builder().status(OfferStatus.IN_PROGRESS).build();
            Offer offer3 = Offer.builder().status(OfferStatus.PROCESSED).build();
            Offer offer4 = Offer.builder().status(OfferStatus.AWAITING_CUSTOMER_DECISION).build();

            // When
            AggregateOfferStatus result1 = mapper.mapRootStatus(offer1);
            AggregateOfferStatus result2 = mapper.mapRootStatus(offer2);
            AggregateOfferStatus result3 = mapper.mapRootStatus(offer3);
            AggregateOfferStatus result4 = mapper.mapRootStatus(offer4);

            // Then
            assertThat(result1).isEqualTo(AggregateOfferStatus.ACTIVE);
            assertThat(result2).isEqualTo(AggregateOfferStatus.ACTIVE);
            assertThat(result3).isEqualTo(AggregateOfferStatus.ACTIVE);
            assertThat(result4).isEqualTo(AggregateOfferStatus.ACTIVE);
        }
    }

    @Nested
    @DisplayName("mapStatusLocalized Tests")
    class MapStatusLocalizedTests {

        @Test
        @DisplayName("Should return description for valid status")
        void shouldReturnDescriptionForValidStatus() {
            // When
            String result = mapper.mapStatusLocalized(OfferStatus.IN_PROGRESS);

            // Then
            assertThat(result).isEqualTo("В работе");
        }

        @Test
        @DisplayName("Should return null for null status")
        void shouldReturnNullForNullStatus() {
            // When
            String result = mapper.mapStatusLocalized(null);

            // Then
            assertThat(result).isNull();
        }
    }

    @Nested
    @DisplayName("mapStatusDescriptionLocalized Tests")
    class MapStatusDescriptionLocalizedTests {

        @Test
        @DisplayName("Should return localized description for valid status")
        void shouldReturnLocalizedDescriptionForValidStatus() {
            // When
            String result = mapper.mapStatusDescriptionLocalized(OfferStatus.IN_PROGRESS);

            // Then
            assertThat(result).isEqualTo("Заполните предложение и отправьте его менеджеру");
        }

        @Test
        @DisplayName("Should return null for null status")
        void shouldReturnNullForNullStatus() {
            // When
            String result = mapper.mapStatusDescriptionLocalized(null);

            // Then
            assertThat(result).isNull();
        }
    }

    @Nested
    @DisplayName("mapOrdersInfo Tests")
    class MapOrdersInfoTests {

        @Test
        @DisplayName("Should map order IDs to OrderInfoDTO list")
        void shouldMapOrderIdsToOrderInfoDTOList() {
            // Given
            List<Long> orderIds = List.of(1L, 2L, 3L);

            // When
            List<OrderInfoDTO> result = mapper.mapOrdersInfo(orderIds);

            // Then
            assertThat(result).hasSize(3);
            assertThat(result.get(0).orderId()).isEqualTo(1L);
            assertThat(result.get(0).state()).isNull();
            assertThat(result.get(1).orderId()).isEqualTo(2L);
            assertThat(result.get(2).orderId()).isEqualTo(3L);
        }

        @Test
        @DisplayName("Should return empty list for null orders")
        void shouldReturnEmptyListForNullOrders() {
            // When
            List<OrderInfoDTO> result = mapper.mapOrdersInfo(null);

            // Then
            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("Should return empty list for empty orders")
        void shouldReturnEmptyListForEmptyOrders() {
            // When
            List<OrderInfoDTO> result = mapper.mapOrdersInfo(List.of());

            // Then
            assertThat(result).isEmpty();
        }
    }

    @Nested
    @DisplayName("mapProposedOffers Tests")
    class MapProposedOffersTests {

        @Test
        @DisplayName("Should map ProposedOffer set to ShopperProposedOfferDTO list")
        void shouldMapProposedOfferSetToShopperProposedOfferDTOList() {
            // Given
            Set<ProposedOffer> proposedOffers = createProposedOffers();

            // When
            List<ShopperProposedOfferDTO> result = mapper.mapProposedOffers(proposedOffers);

            // Then
            assertThat(result).hasSize(2);
            assertThat(result.get(0).id()).isIn(1L, 2L);
            assertThat(result.get(1).id()).isIn(1L, 2L);
        }

        @Test
        @DisplayName("Should return empty list for null proposed offers")
        void shouldReturnEmptyListForNullProposedOffers() {
            // When
            List<ShopperProposedOfferDTO> result = mapper.mapProposedOffers(null);

            // Then
            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("Should return empty list for empty proposed offers")
        void shouldReturnEmptyListForEmptyProposedOffers() {
            // When
            List<ShopperProposedOfferDTO> result = mapper.mapProposedOffers(new HashSet<>());

            // Then
            assertThat(result).isEmpty();
        }
    }

    @Nested
    @DisplayName("mapProposedOffer Tests")
    class MapProposedOfferTests {

        @Test
        @DisplayName("Should map ProposedOffer to ShopperProposedOfferDTO successfully")
        void shouldMapProposedOfferToShopperProposedOfferDTO() {
            // Given
            ProposedOffer proposedOffer = createSingleProposedOffer();

            // When
            ShopperProposedOfferDTO result = mapper.mapProposedOffer(proposedOffer);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.id()).isEqualTo(1L);
            assertThat(result.deliveryDate()).isEqualTo(proposedOffer.getDeliveryDate());
            assertThat(result.validUntil()).isEqualTo(proposedOffer.getValidUntil());
            assertThat(result.timezone()).isEqualTo(proposedOffer.getValidUntil().getZone().getId());
            assertThat(result.hasReceipt()).isTrue();
            assertThat(result.isCompleteSet()).isTrue();
            assertThat(result.comment()).isEqualTo("Test comment");
            assertThat(result.condition()).isNotNull();
            assertThat(result.condition().id()).isEqualTo(1L);
            assertThat(result.prices()).isNotNull();
        }
    }

    @Nested
    @DisplayName("mapTimezone Tests")
    class MapTimezoneTests {

        @Test
        @DisplayName("Should extract timezone from ZonedDateTime")
        void shouldExtractTimezoneFromZonedDateTime() {
            // Given
            ZonedDateTime dateTime = ZonedDateTime.now(ZoneId.of("Europe/Moscow"));

            // When
            String result = mapper.mapTimezone(dateTime);

            // Then
            assertThat(result).isEqualTo("Europe/Moscow");
        }
    }

    @Nested
    @DisplayName("mapProposedOfferStatusDescription Tests")
    class MapProposedOfferStatusDescriptionTests {

        @Test
        @DisplayName("Should return sent to customer message when isSentToCustomer is true")
        void shouldReturnSentToCustomerMessage() {
            // Given
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .isSentToCustomer(true)
                    .build();

            // When
            String result = mapper.mapProposedOfferStatusDescription(proposedOffer);

            // Then
            assertThat(result).isEqualTo("Это предложение было выбрано менеджером для отправки покупателю");
        }

        @Test
        @DisplayName("Should return null when isSentToCustomer is false")
        void shouldReturnNullWhenNotSentToCustomer() {
            // Given
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .isSentToCustomer(false)
                    .build();

            // When
            String result = mapper.mapProposedOfferStatusDescription(proposedOffer);

            // Then
            assertThat(result).isNull();
        }

        @Test
        @DisplayName("Should return null when isSentToCustomer is null")
        void shouldReturnNullWhenIsSentToCustomerIsNull() {
            // Given
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .isSentToCustomer(null)
                    .build();

            // When
            String result = mapper.mapProposedOfferStatusDescription(proposedOffer);

            // Then
            assertThat(result).isNull();
        }
    }

    @Nested
    @DisplayName("mapPriceInfo Tests")
    class MapPriceInfoTests {

        @Test
        @DisplayName("Should calculate prices with commission correctly")
        void shouldCalculatePricesWithCommissionCorrectly() {
            // Given
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .currency(Currency.EUR)
                    .currencyPrice(BigDecimal.valueOf(100))
                    .rublePrice(BigDecimal.valueOf(9000))
                    .commission(BigDecimal.valueOf(15))
                    .build();

            // When
            PriceInfoDTO result = mapper.mapPriceInfo(proposedOffer);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.currency()).isEqualTo(Currency.EUR);
            assertThat(result.currencyPrice()).isEqualTo(BigDecimal.valueOf(100));
            assertThat(result.rublePrice()).isEqualTo(BigDecimal.valueOf(9000));
            assertThat(result.currencyPriceWithCommission()).isEqualByComparingTo(BigDecimal.valueOf(115.00));
            assertThat(result.localPriceWithCommission()).isEqualByComparingTo(BigDecimal.valueOf(10350.00));
        }

        @Test
        @DisplayName("Should handle null commission")
        void shouldHandleNullCommission() {
            // Given
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .currency(Currency.EUR)
                    .currencyPrice(BigDecimal.valueOf(100))
                    .rublePrice(BigDecimal.valueOf(9000))
                    .commission(null)
                    .build();

            // When
            PriceInfoDTO result = mapper.mapPriceInfo(proposedOffer);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.currency()).isEqualTo(Currency.EUR);
            assertThat(result.currencyPrice()).isEqualTo(BigDecimal.valueOf(100));
            assertThat(result.rublePrice()).isEqualTo(BigDecimal.valueOf(9000));
            assertThat(result.currencyPriceWithCommission()).isNull();
            assertThat(result.localPriceWithCommission()).isNull();
        }

        @Test
        @DisplayName("Should handle null prices")
        void shouldHandleNullPrices() {
            // Given
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .currency(Currency.EUR)
                    .currencyPrice(null)
                    .rublePrice(null)
                    .commission(BigDecimal.valueOf(15))
                    .build();

            // When
            PriceInfoDTO result = mapper.mapPriceInfo(proposedOffer);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.currency()).isEqualTo(Currency.EUR);
            assertThat(result.currencyPrice()).isNull();
            assertThat(result.rublePrice()).isNull();
            assertThat(result.currencyPriceWithCommission()).isNull();
            assertThat(result.localPriceWithCommission()).isNull();
        }
    }

    @Nested
    @DisplayName("mapProductCondition Tests")
    class MapProductConditionTests {

        @Test
        @DisplayName("Should map product condition ID to ProductConditionDTO")
        void shouldMapProductConditionIdToProductConditionDTO() {
            // Given
            Long productConditionId = 1L;

            // When
            ProductConditionDTO result = mapper.mapProductCondition(productConditionId);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.id()).isEqualTo(1L);
            assertThat(result.name()).isNull();
            assertThat(result.description()).isNull();
        }

        @Test
        @DisplayName("Should return null for null product condition ID")
        void shouldReturnNullForNullProductConditionId() {
            // When
            ProductConditionDTO result = mapper.mapProductCondition(null);

            // Then
            assertThat(result).isNull();
        }
    }

    // Helper methods for creating test data
    private Offer createCompleteOffer() {
        ZonedDateTime now = ZonedDateTime.now();

        ProposedOffer proposedOffer = ProposedOffer.builder()
                .id(1L)
                .rublePrice(BigDecimal.valueOf(50000))
                .currencyPrice(BigDecimal.valueOf(500))
                .currency(Currency.EUR)
                .commission(BigDecimal.valueOf(15))
                .deliveryDate(now.plusDays(7))
                .validUntil(now.plusDays(14))
                .hasReceipt(true)
                .isCompleteSet(true)
                .comment("Test comment")
                .productConditionId(1L)
                .isSentToCustomer(false)
                .build();

        Set<ProposedOffer> proposedOffers = new HashSet<>();
        proposedOffers.add(proposedOffer);

        PurchaseOrder purchaseOrder = PurchaseOrder.builder()
                .orders(List.of(1L, 2L))
                .build();

        Shipment shipment = Shipment.builder()
                .id(1L)
                .categoryName("Electronics")
                .brandName("Apple")
                .materialAttributeName("Metal")
                .colorAttributeName("Black")
                .modelName("iPhone 15")
                .description("Test description")
                .images(List.of(new ImageDTO(UUID.randomUUID(), "http://example.com/image.jpg", ZonedDateTime.now())))
                .links(List.of("http://example.com"))
                .shipmentSize(new ShimpentSizeDTO("EU", 42L, Set.of("42", "43", "44")))
                .purchaseOrder(purchaseOrder)
                .build();

        return Offer.builder()
                .id(1L)
                .status(OfferStatus.IN_PROGRESS)
                .type(OfferType.BUYER_OFFER)
                .sellerId(100L)
                .validUntil(now.plusDays(30))
                .creationDate(now.minusDays(1))
                .shipment(shipment)
                .proposedOffers(proposedOffers)
                .build();
    }

    private Shipment createMinimalShipment() {
        return Shipment.builder()
                .id(1L)
                .purchaseOrder(PurchaseOrder.builder().build())
                .build();
    }

    private Set<ProposedOffer> createProposedOffers() {
        ZonedDateTime now = ZonedDateTime.now();

        ProposedOffer offer1 = ProposedOffer.builder()
                .id(1L)
                .deliveryDate(now.plusDays(7))
                .validUntil(now.plusDays(14))
                .hasReceipt(true)
                .isCompleteSet(true)
                .productConditionId(1L)
                .build();

        ProposedOffer offer2 = ProposedOffer.builder()
                .id(2L)
                .deliveryDate(now.plusDays(10))
                .validUntil(now.plusDays(20))
                .hasReceipt(false)
                .isCompleteSet(false)
                .productConditionId(2L)
                .build();

        Set<ProposedOffer> offers = new HashSet<>();
        offers.add(offer1);
        offers.add(offer2);
        return offers;
    }

    private ProposedOffer createSingleProposedOffer() {
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("Europe/Moscow"));

        return ProposedOffer.builder()
                .id(1L)
                .currency(Currency.EUR)
                .currencyPrice(BigDecimal.valueOf(100))
                .rublePrice(BigDecimal.valueOf(9000))
                .commission(BigDecimal.valueOf(15))
                .deliveryDate(now.plusDays(7))
                .validUntil(now.plusDays(14))
                .hasReceipt(true)
                .isCompleteSet(true)
                .comment("Test comment")
                .productConditionId(1L)
                .isSentToCustomer(false)
                .build();
    }
}
