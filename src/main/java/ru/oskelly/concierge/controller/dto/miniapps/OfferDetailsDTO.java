package ru.oskelly.concierge.controller.dto.miniapps;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;
import ru.oskelly.concierge.data.model.enums.OfferStatus;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * DTO для деталей оффера в miniapps
 */
@Schema(description = "Детали оффера для мини-приложений")
public record OfferDetailsDTO(
        @Schema(description = "Уникальный идентификатор оффера", example = "123")
        Long id,

        // Маппинг реализован в OfferDetailsMapper
        @Schema(description = "Корневой статус оффера", implementation = AggregateOfferStatus.class)
        AggregateOfferStatus aggregateStatus,

        @Schema(description = "Текущий статус оффера", implementation = OfferStatus.class)
        OfferStatus status,

        @Schema(description = "Локализованное название статуса", example = "Ожидает ответа клиента")
        String statusLocalized,

        @Schema(description = "Локализованное описание статуса", 
                example = "Предложение отправлено клиенту и ожидается его решение")
        String statusDescriptionLocalized,

        @Schema(description = "Дата истечения срока действия оффера", 
                example = "2023-12-31T23:59:59+03:00")
        ZonedDateTime expirationDate,

        @Schema(description = "Информация о связанных заказах")
        @ArraySchema(schema = @Schema(implementation = OrderInfoDTO.class))
        List<OrderInfoDTO> ordersInfo,

        @Schema(description = "Предложения от шоперов (ограничено 2)")
        @ArraySchema(schema = @Schema(implementation = ShopperProposedOfferDTO.class))
        List<ShopperProposedOfferDTO> proposedOffers,

        @Schema(description = "Дата создания оффера", example = "2023-11-25T15:30:00+03:00")
        ZonedDateTime creationDate,

        // Информация из шипмента
        @Schema(description = "Название категории товара", example = "Одежда")
        String categoryName,

        @Schema(description = "Название бренда", example = "Nike")
        String brandName,

        @Schema(description = "Название материала", example = "Хлопок")
        String materialAttributeName,

        @Schema(description = "Название цвета", example = "Черный")
        String colorAttributeName,

        @Schema(description = "Название модели", example = "Air Max 90")
        String modelName,

        @Schema(description = "Размер товара", implementation = ShimpentSizeDTO.class)
        ShimpentSizeDTO size,

        @Schema(description = "Описание товара", example = "Кроссовки Nike Air Max 90 в черном цвете")
        String description,

        @Schema(description = "Изображения товара")
        @ArraySchema(schema = @Schema(implementation = ImageDTO.class))
        List<ImageDTO> images,

        @Schema(description = "Ссылки на товар")
        @ArraySchema(schema = @Schema(implementation = String.class))
        List<String> links
) {
}
